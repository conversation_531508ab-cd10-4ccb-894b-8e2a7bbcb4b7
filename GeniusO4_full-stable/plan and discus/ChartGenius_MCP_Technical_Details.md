# ChartGenius MCP Integration - Технические детали конфигурации

## 1. Конфигурация n8n для MCP интеграции

### 1.1 Настройка MCP серверов в n8n
```json
{
  "mcpConfig": {
    "servers": {
      "github": {
        "command": "npx",
        "args": ["-y", "@smithery/github-mcp-server"],
        "env": {
          "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}",
          "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
        }
      },
      "sentry": {
        "command": "npx", 
        "args": ["-y", "@smithery/sentry-mcp-server"],
        "env": {
          "SENTRY_AUTH_TOKEN": "${SENTRY_TOKEN}",
          "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
        }
      },
      "docker": {
        "command": "npx",
        "args": ["-y", "@smithery/docker-mcp-server"],
        "env": {
          "DOCKER_HOST": "unix:///var/run/docker.sock",
          "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
        }
      },
      "grafana": {
        "command": "npx",
        "args": ["-y", "@smithery/grafana-mcp-server"],
        "env": {
          "GRAFANA_URL": "https://chartgenius.grafana.net",
          "GRAFANA_API_KEY": "${GRAFANA_API_KEY}",
          "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
        }
      },
      "figma": {
        "command": "npx",
        "args": ["-y", "@smithery/figma-mcp-server"],
        "env": {
          "FIGMA_ACCESS_TOKEN": "${FIGMA_ACCESS_TOKEN}",
          "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
        }
      }
    }
  }
}
```

### 1.2 Environment Variables для Ubuntu сервера (*************)
```bash
# /home/<USER>/.env
SMITHERY_API_KEY=your_smithery_api_key_here
GITHUB_TOKEN=ghp_your_github_token_here
SENTRY_TOKEN=your_sentry_token_here
GRAFANA_API_KEY=your_grafana_api_key_here
FIGMA_ACCESS_TOKEN=your_figma_token_here

# Oracle Database ChartGenius2
ORACLE_CONNECTION_STRING=your_oracle_connection_string
ORACLE_USERNAME=your_username
ORACLE_PASSWORD=your_password

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
ADMIN_TELEGRAM_ID=299820674

# Redis Cache
REDIS_URL=redis://localhost:6379

# FastAPI Backend
FASTAPI_URL=https://chartgenius.ru/api
FASTAPI_SECRET_KEY=your_secret_key

# CryptoCompare API
CRYPTOCOMPARE_API_KEY=your_cryptocompare_key

# LLM Providers
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
```

## 2. Миграция Telegram Bot логики в n8n

### 2.1 Workflow: Telegram Command Handler
```json
{
  "name": "Telegram Bot Commands",
  "active": true,
  "nodes": [
    {
      "id": "webhook-trigger",
      "name": "Telegram Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "parameters": {
        "path": "telegram-bot",
        "httpMethod": "POST",
        "responseMode": "responseNode"
      }
    },
    {
      "id": "command-router",
      "name": "Command Router",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 1,
      "position": [460, 300],
      "parameters": {
        "dataType": "string",
        "value1": "={{ $json.message.text }}",
        "rules": {
          "rules": [
            {
              "value2": "/start",
              "operation": "startsWith"
            },
            {
              "value2": "/watch",
              "operation": "startsWith"
            },
            {
              "value2": "/stats",
              "operation": "startsWith"
            },
            {
              "value2": "/ban",
              "operation": "startsWith"
            },
            {
              "value2": "/gc",
              "operation": "startsWith"
            }
          ]
        }
      }
    },
    {
      "id": "start-handler",
      "name": "Handle Start Command",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 200],
      "parameters": {
        "functionCode": "// Обработка команды /start\nconst userId = items[0].json.message.from.id;\nconst userName = items[0].json.message.from.first_name;\nconst chatId = items[0].json.message.chat.id;\n\n// Создание или обновление пользователя в БД\nreturn [{\n  json: {\n    chat_id: chatId,\n    text: `Привет, ${userName}! 👋\\n\\nДобро пожаловать в ChartGenius!\\n\\n🚀 Откройте терминал для анализа криптовалют\\n📊 Получайте профессиональные торговые сигналы\\n💎 Доступ к 25+ техническим индикаторам`,\n    reply_markup: {\n      inline_keyboard: [[\n        {\n          text: \"🚀 Открыть терминал\",\n          web_app: { url: \"https://chartgenius.ru\" }\n        }\n      ], [\n        {\n          text: \"📊 Помощь\",\n          callback_data: \"help\"\n        },\n        {\n          text: \"⚙️ Настройки\",\n          callback_data: \"settings\"\n        }\n      ]]\n    }\n  }\n}];"
      }
    },
    {
      "id": "watch-handler",
      "name": "Handle Watch Command",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 300],
      "parameters": {
        "functionCode": "// Обработка команды /watch\nconst userId = items[0].json.message.from.id;\nconst chatId = items[0].json.message.chat.id;\nconst messageText = items[0].json.message.text;\n\n// Извлекаем символ из команды /watch BTC\nconst parts = messageText.split(' ');\nif (parts.length < 2) {\n  return [{\n    json: {\n      chat_id: chatId,\n      text: \"❌ Укажите символ криптовалюты\\n\\nПример: /watch BTC\"\n    }\n  }];\n}\n\nconst symbol = parts[1].toUpperCase();\n\nreturn [{\n  json: {\n    chat_id: chatId,\n    user_id: userId,\n    symbol: symbol,\n    text: `✅ Добавлен в отслеживание: ${symbol}\\n\\n📊 Вы будете получать уведомления о важных изменениях цены и сигналах.`,\n    action: \"add_to_watchlist\"\n  }\n}];"
      }
    },
    {
      "id": "stats-handler",
      "name": "Handle Stats Command",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 400],
      "parameters": {
        "functionCode": "// Обработка команды /stats\nconst userId = items[0].json.message.from.id;\nconst chatId = items[0].json.message.chat.id;\n\nreturn [{\n  json: {\n    chat_id: chatId,\n    user_id: userId,\n    action: \"get_user_stats\"\n  }\n}];"
      }
    },
    {
      "id": "ban-handler",
      "name": "Handle Ban Command",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 500],
      "parameters": {
        "functionCode": "// Обработка команды /ban (только для админов)\nconst userId = items[0].json.message.from.id;\nconst chatId = items[0].json.message.chat.id;\nconst messageText = items[0].json.message.text;\n\n// Проверка прав администратора\nif (userId !== 299820674) {\n  return [{\n    json: {\n      chat_id: chatId,\n      text: \"❌ У вас нет прав для выполнения этой команды\"\n    }\n  }];\n}\n\n// Извлекаем ID пользователя для бана\nconst parts = messageText.split(' ');\nif (parts.length < 2) {\n  return [{\n    json: {\n      chat_id: chatId,\n      text: \"❌ Укажите ID пользователя\\n\\nПример: /ban 123456789\"\n    }\n  }];\n}\n\nconst targetUserId = parseInt(parts[1]);\n\nreturn [{\n  json: {\n    chat_id: chatId,\n    admin_id: userId,\n    target_user_id: targetUserId,\n    action: \"ban_user\"\n  }\n}];"
      }
    },
    {
      "id": "gc-handler",
      "name": "Handle GC Command",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 600],
      "parameters": {
        "functionCode": "// Обработка команды /gc (ChartGenius анализ)\nconst userId = items[0].json.message.from.id;\nconst chatId = items[0].json.message.chat.id;\nconst messageText = items[0].json.message.text;\n\n// Извлекаем символ из команды /gc BTC\nconst parts = messageText.split(' ');\nif (parts.length < 2) {\n  return [{\n    json: {\n      chat_id: chatId,\n      text: \"❌ Укажите символ криптовалюты\\n\\nПример: /gc BTC\"\n    }\n  }];\n}\n\nconst symbol = parts[1].toUpperCase();\n\nreturn [{\n  json: {\n    chat_id: chatId,\n    user_id: userId,\n    symbol: symbol,\n    action: \"generate_analysis\"\n  }\n}];"
      }
    },
    {
      "id": "send-response",
      "name": "Send Telegram Response",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [900, 300],
      "parameters": {
        "url": "=https://api.telegram.org/bot{{ $env.TELEGRAM_BOT_TOKEN }}/sendMessage",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "chat_id",
              "value": "={{ $json.chat_id }}"
            },
            {
              "name": "text",
              "value": "={{ $json.text }}"
            },
            {
              "name": "reply_markup",
              "value": "={{ $json.reply_markup }}"
            },
            {
              "name": "parse_mode",
              "value": "HTML"
            }
          ]
        }
      }
    }
  ],
  "connections": {
    "webhook-trigger": {
      "main": [[{ "node": "command-router", "type": "main", "index": 0 }]]
    },
    "command-router": {
      "main": [
        [{ "node": "start-handler", "type": "main", "index": 0 }],
        [{ "node": "watch-handler", "type": "main", "index": 0 }],
        [{ "node": "stats-handler", "type": "main", "index": 0 }],
        [{ "node": "ban-handler", "type": "main", "index": 0 }],
        [{ "node": "gc-handler", "type": "main", "index": 0 }]
      ]
    },
    "start-handler": {
      "main": [[{ "node": "send-response", "type": "main", "index": 0 }]]
    },
    "watch-handler": {
      "main": [[{ "node": "send-response", "type": "main", "index": 0 }]]
    },
    "stats-handler": {
      "main": [[{ "node": "send-response", "type": "main", "index": 0 }]]
    },
    "ban-handler": {
      "main": [[{ "node": "send-response", "type": "main", "index": 0 }]]
    },
    "gc-handler": {
      "main": [[{ "node": "send-response", "type": "main", "index": 0 }]]
    }
  }
}
```

### 2.2 Workflow: Payment Processing (Telegram Stars)
```json
{
  "name": "Telegram Stars Payment",
  "active": true,
  "nodes": [
    {
      "id": "payment-webhook",
      "name": "Payment Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "parameters": {
        "path": "telegram-payment",
        "httpMethod": "POST"
      }
    },
    {
      "id": "validate-payment",
      "name": "Validate Payment",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [460, 300],
      "parameters": {
        "functionCode": "// Валидация платежа Telegram Stars\nconst payment = items[0].json.pre_checkout_query;\nif (!payment) {\n  return [{ json: { valid: false, error: 'No payment data' } }];\n}\n\nconst payload = payment.invoice_payload;\nconst amount = payment.total_amount;\nconst userId = payment.from.id;\n\n// Проверяем payload и сумму\nif (payload === 'subscription_premium' && amount === 100) {\n  return [{ json: { \n    valid: true, \n    type: 'premium', \n    user_id: userId,\n    amount: amount,\n    duration_days: 30\n  } }];\n} else if (payload === 'subscription_vip' && amount === 300) {\n  return [{ json: { \n    valid: true, \n    type: 'vip', \n    user_id: userId,\n    amount: amount,\n    duration_days: 30\n  } }];\n} else {\n  return [{ json: { valid: false, error: 'Invalid payload or amount' } }];\n}"
      }
    },
    {
      "id": "process-subscription",
      "name": "Process Subscription",
      "type": "n8n-nodes-base.oracle",
      "typeVersion": 1,
      "position": [680, 300],
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE users SET subscription_type = :1, subscription_expires = :2, updated_at = CURRENT_TIMESTAMP WHERE telegram_id = :3",
        "additionalFields": {
          "values": "={{ [$json.type, new Date(Date.now() + $json.duration_days * 24 * 60 * 60 * 1000).toISOString(), $json.user_id] }}"
        }
      }
    }
  ]
}
```

## 3. Интеграция с Oracle AJD через n8n

### 3.1 Workflow: Market Data Storage
```json
{
  "name": "Store Market Data",
  "active": true,
  "nodes": [
    {
      "id": "schedule-trigger",
      "name": "Every Minute",
      "type": "n8n-nodes-base.cron",
      "typeVersion": 1,
      "position": [240, 300],
      "parameters": {
        "cronExpression": "* * * * *"
      }
    },
    {
      "id": "fetch-crypto-data",
      "name": "Fetch Crypto Prices",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [460, 300],
      "parameters": {
        "url": "https://api.cryptocompare.com/data/pricemulti",
        "qs": {
          "fsyms": "BTC,ETH,ADA,DOT,LINK,MATIC,SOL,AVAX,UNI,AAVE",
          "tsyms": "USD,EUR,RUB",
          "api_key": "={{ $env.CRYPTOCOMPARE_API_KEY }}"
        }
      }
    },
    {
      "id": "transform-data",
      "name": "Transform Data",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [680, 300],
      "parameters": {
        "functionCode": "// Преобразование данных для Oracle\nconst data = items[0].json;\nconst results = [];\nconst timestamp = new Date().toISOString();\n\nfor (const [symbol, prices] of Object.entries(data)) {\n  for (const [currency, price] of Object.entries(prices)) {\n    results.push({\n      symbol: symbol,\n      currency: currency,\n      price: price,\n      created_at: timestamp\n    });\n  }\n}\n\nreturn results.map(item => ({ json: item }));"
      }
    },
    {
      "id": "store-oracle",
      "name": "Store in Oracle",
      "type": "n8n-nodes-base.oracle",
      "typeVersion": 1,
      "position": [900, 300],
      "parameters": {
        "operation": "insert",
        "table": "market_data",
        "columns": "symbol,currency,price,created_at",
        "additionalFields": {
          "values": "={{ [$json.symbol, $json.currency, $json.price, $json.created_at] }}"
        }
      }
    }
  ]
}
```
