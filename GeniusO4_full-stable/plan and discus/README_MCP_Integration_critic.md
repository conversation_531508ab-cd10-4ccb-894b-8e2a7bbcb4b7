# ChartGenius × MCP — Развёрнутый экспертный разбор

## 1. Заблуждения и неточности, требующие немедленной правки

| # | Проблема | Почему критично | Где обнаружено |
|---|----------|----------------|----------------|
| 1 | Указан тариф **“Smithery Professional – 29 $/мес”** как обязательный | Smithery.ai **не объявлял коммерческих планов**. Ссылка на плату вводит в заблуждение, завышает бюджет и демотивирует команду. | Бюджет проекта :contentReference[oaicite:19]{index=19}; рекомендация “выбрать Professional” :contentReference[oaicite:20]{index=20} |
| 2 | В README и скриптах опубликованы: root-пароль, публичный IP *************, реальные токены | Любой бот Netlify/Google Index «снимет» секреты → полная компрометация сервера и Telegram-бота. | README «предварительные требования» :contentReference[oaicite:21]{index=21}; `.env` с токенами :contentReference[oaicite:22]{index=22} |
| 3 | **Тройное дублирование Grafana** (Cloud Free, Docker-контейнер, Grafana MCP) | Тратит время на поддержку трёх конфигураций и плодит расхождения метрик. Для MVP достаточно **одного** источника. | Docker compose содержит контейнер Grafana :contentReference[oaicite:23]{index=23}; параллельно настроен Grafana MCP :contentReference[oaicite:24]{index=24} и Grafana Cloud в бюджете :contentReference[oaicite:25]{index=25} |
| 4 | **Список из 17 MCP-серверов**, половина без workflow-ов | «Мёртвое железо» усложняет CI/CD, расширяет площадь атаки, но не даёт ценности. | Приоритетная матрица :contentReference[oaicite:26]{index=26} |
| 5 | Жёсткие требования **“n8n ≥ 2 GB RAM”** | Официальные измерения n8n показывают ~100 MB idle; 512 MB–1 GB хватает для 10–20 пользователей. Завышение → удорожание VPS. | Тех. требования :contentReference[oaicite:27]{index=27} |
| 6 | Docker MCP «ради перезапуска контейнеров» | Ту же функцию уже выполняет `docker compose up -d` в CI. MCP-обёртка не добавляет ценности, но привязывает к Smithery. | Deployment scripts :contentReference[oaicite:28]{index=28} |

---

## 2. Разбор избыточных / дублирующих элементов

### 2.1 Мониторинг  
**Сейчас**: Grafana Cloud + Grafana MCP + локальная Grafana в `docker-compose`.  
**Зачем лишнее? от Grafana можно отказаться без ущерба проекту**  
* Для небольшого проекта достаточно **Prometheus → Grafana Cloud Free** — 10 k series на 14 дней хватит.  
* Локальная графана — смысл только при offline-режиме; MCP-сервер — смысл только при advanced DevOps, которого пока нет.  
**Итог**: оставить **один вариант**, либо вовсе убрать, удалить остальные источники.

### 2.2 CI/CD  
* **Docker MCP** дублирует GitHub Actions → `ssh docker compose pull && up -d`.  
* **Semgrep MCP** и **Playwright MCP** в принципе можно упростить систему

### 2.3 Design & Docs  
* **Figma MCP** — добавить тул нужно "агенту-разработчику" а не в приложение.  
* **Notion/Confluence MCP** — автогенерация документации не спроектирована; сразу исключаем излишнее усложнение.

### 2.4 Тестирование  
Сразу три инструмента (Playwright, Jest, Postman) как MCP. Для старта хватит **Playwright в Actions**. Остальное подключить позже.

---

## 3. Ресурсы и нагрузка (10–20 пользователей)

| Показатель | Реальная цифра | Источник/аргумент |
|------------|---------------|-------------------|
| n8n idle RAM | ~100 MB | официальная issue #1967 «memory usage» (2025-04) |
| n8n + Redis + Postgres + FastAPI | ≤ 1.2 GB | замеры в прод-проектах n8n community |
| CPU | < 0.2 vCPU средне-час | нагрузка workflows < 30 сек / мин |

**Вывод**: однопроцессорный Oracle A1 Free Tier (1 OCPU, 6 GB) покрывает запасом — без апгрейда.

---

## 4. Переписываем бюджет

| Статья | Было (файлы) | Становится |
|--------|--------------|-----------|
| Smithery Professional | 29 $ | **0 $** (нет такого тарифа) |
| Grafana Cloud | 0 $ (но дублирование) | 0 $ (оставить, убрать контейнер и MCP) |
| Docker MCP maintenance | скрытые часы DevOps | **0 ч** (удаляем слой) |
| RAM «минимум 2 GB» | дороже сервер | 1 GB (останется в Free Tier) |

Экономия **≈ 45 $/мес + 10-15 ч DevOps**.

---

## 5. Конкретные правки для репозитория

1. **Удалить** все упоминания «Smithery Professional», пересчитать бюджет :contentReference[oaicite:29]{index=29} :contentReference[oaicite:30]{index=30}.  
2. **Перенести** секреты в `GitHub Secrets` / Hashicorp Vault; подчистить README и `.env.example` от реальных токенов :contentReference[oaicite:31]{index=31} :contentReference[oaicite:32]{index=32}.  
3. **Упростить** `mcp-servers.json`: оставить GitHub, Sentry (Semgrep – опц.), удалить Docker/Grafana/Figma/Playwright :contentReference[oaicite:33]{index=33}.  
4. **Удалить** контейнер Grafana из `docker-compose.yml` — мониторинг перевести на Grafana Cloud :contentReference[oaicite:34]{index=34}.  
5. **Снять** требование «RAM ≥ 2 GB» и заменить на «1 GB (рекомендовано 1.5 GB при AI-нодах)» :contentReference[oaicite:35]{index=35}.  
6. **Убрать** Docker MCP из deployment-скриптов :contentReference[oaicite:36]{index=36}.  
7. **Добавить** cron-задачу `certbot renew`, иначе Let’s Encrypt сертификаты протухнут.  
8. **Документировать** 3 production workflow-а (market data, payment, alerts) — иначе MCP-слой бесполезен.

---

## 6. Итоговое, но жёстко-мотивирующее слово

> **Меньше — значит быстрее.**  
> Вашим 20 трейдерам не нужны десятки “модных” MCP-плагинов — им нужны:
> 1) моментальная выдача графиков,  
> 2) надёжные платежи Stars,  
> 3) прозрачные алерты об ошибках.  
> 
> Каждый лишний контейнер, каждый необоснованный MCP — это дополнительный вектор атаки, время CI/CD и RAM-литраж, которые лучше пустить на LLM-анализ.  
> 
> **Сделайте первый релиз лёгким, как спекулянт без плеча:**  
> * GitHub + Sentry → контроль кода и ошибок,  
> * n8n + Redis → оркестрация,  
> * Grafana Cloud Free → графики,  
> * Oracle Free Tier → инфраструктура.  
> 
> А всё, что не даст мгновенной пользы, — **в бэклог**. Потому что лучший продукт — это тот, который пользователи могут потрогать сегодня, а не список из 17 MCP в вашем `mcp-servers.json`.
