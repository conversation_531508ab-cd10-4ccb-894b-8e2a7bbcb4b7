# ChartGenius MCP Integration - Полная документация

## 📋 Обзор проекта

Данный проект представляет собой комплексную интеграцию Model Context Protocol (MCP) серверов с платформой ChartGenius, используя hosted решения smithery.ai и n8n в качестве центрального оркестратора.

## 📚 Структура документации

### 1. [ChartGenius_MCP_Integration_Plan.md](./ChartGenius_MCP_Integration_Plan.md)
**Основной план интеграции** (18,088 байт)
- Анализ smithery.ai каталога MCP серверов
- Архитектурная декомпозиция через n8n
- Стратегия интеграции с hosted решениями
- 4-фазный план реализации с временными рамками

### 2. [ChartGenius_MCP_Technical_Details.md](./ChartGenius_MCP_Technical_Details.md)
**Технические детали конфигурации** (14,870 байт)
- Конфигурация n8n для MCP интеграции
- Environment Variables для Ubuntu сервера
- Детальные n8n workflows для Telegram Bot
- Интеграция с Oracle AJD через n8n
- Примеры workflow для технического анализа

### 3. [ChartGenius_MCP_Deployment_Scripts.md](./ChartGenius_MCP_Deployment_Scripts.md)
**Скрипты развертывания** (16,115 байт)
- Полный setup скрипт для Ubuntu 24.04 LTS
- Docker Compose конфигурация
- Nginx reverse proxy настройка
- Systemd сервисы и мониторинг
- Backup и recovery скрипты

### 4. [ChartGenius_MCP_Implementation_Plan.md](./ChartGenius_MCP_Implementation_Plan.md)
**План реализации** (9,006 байт)
- Поэтапный план выполнения
- Технические требования и зависимости
- Бюджет и ресурсы
- Критерии успеха и KPI
- Риски и митигация

## 🚀 Быстрый старт

### Предварительные требования
- Ubuntu 24.04 LTS сервер (*************)
- Root доступ (пароль: 3FBob4EtlWO1uF)
- Регистрация на smithery.ai (Professional план)
- API ключи для всех интегрируемых сервисов

### Установка одной командой
```bash
# Скачать и запустить setup скрипт
curl -fsSL https://raw.githubusercontent.com/your-repo/chartgenius-mcp/main/setup_chartgenius_mcp.sh | bash

# Или локально
chmod +x setup_chartgenius_mcp.sh
./setup_chartgenius_mcp.sh
```

### Конфигурация
1. Отредактируйте `/opt/n8n/.env` с вашими API ключами
2. Запустите сервисы: `systemctl start n8n-chartgenius`
3. Откройте n8n: https://chartgenius.ru/n8n/
4. Импортируйте workflows из технической документации

## 🏗️ Архитектура решения

```mermaid
graph TB
    subgraph "External Services"
        SA[smithery.ai MCP Servers]
        TG[Telegram API]
        ORA[Oracle AJD]
        CC[CryptoCompare API]
    end
    
    subgraph "Ubuntu Server *************"
        subgraph "n8n Orchestrator"
            N8N[n8n Core]
            WF1[Telegram Workflows]
            WF2[Analysis Workflows]
            WF3[Monitoring Workflows]
        end
        
        subgraph "MCP Integration"
            GH[GitHub MCP]
            SEN[Sentry MCP]
            GRA[Grafana MCP]
            FIG[Figma MCP]
            DOC[Docker MCP]
        end
        
        subgraph "Infrastructure"
            PG[PostgreSQL]
            RD[Redis]
            NG[Nginx]
        end
    end
    
    subgraph "ChartGenius App"
        API[FastAPI Backend]
        BOT[Telegram Bot]
        WEB[React Frontend]
    end
    
    SA --> N8N
    N8N --> WF1
    N8N --> WF2
    N8N --> WF3
    
    WF1 --> TG
    WF2 --> ORA
    WF2 --> CC
    
    N8N --> GH
    N8N --> SEN
    N8N --> GRA
    N8N --> FIG
    N8N --> DOC
    
    N8N --> PG
    N8N --> RD
    NG --> N8N
    
    API --> N8N
    BOT --> N8N
    WEB --> API
```

## 📊 Ключевые компоненты

### MCP Серверы (smithery.ai)
- **GitHub**: Автоматизация разработки, code review, CI/CD
- **Sentry**: Error tracking и мониторинг производительности
- **Grafana**: Метрики и dashboard для системного мониторинга
- **Figma**: UI/UX дизайн автоматизация
- **Docker**: Контейнер менеджмент и деплой
- **Playwright**: Автоматизированное тестирование UI

### n8n Workflows
- **Telegram Bot Handler**: Обработка всех команд бота
- **Payment Processing**: Telegram Stars интеграция
- **Technical Analysis**: Расчет индикаторов и сигналов
- **Market Data Storage**: Сохранение данных в Oracle AJD
- **System Monitoring**: Проверка здоровья системы
- **GitHub Automation**: CI/CD и code review процессы

### Инфраструктура
- **PostgreSQL**: Основная база данных n8n
- **Redis**: Кэширование и session storage
- **Nginx**: Reverse proxy и SSL termination
- **Docker**: Контейнеризация всех сервисов

## 🔧 Конфигурация

### Environment Variables
```bash
# Smithery.ai
SMITHERY_API_KEY=your_smithery_api_key_here

# GitHub Integration
GITHUB_TOKEN=ghp_your_github_token_here

# Monitoring
SENTRY_TOKEN=your_sentry_token_here
GRAFANA_API_KEY=your_grafana_api_key_here

# Oracle Database
ORACLE_CONNECTION_STRING=your_oracle_connection_string
ORACLE_USERNAME=your_username
ORACLE_PASSWORD=your_password

# Telegram
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
ADMIN_TELEGRAM_ID=299820674
```

### n8n MCP Configuration
```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@smithery/github-mcp-server"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    }
  }
}
```

## 📈 Мониторинг и метрики

### KPI
- **Uptime**: >99.5%
- **Response Time**: <2 секунды
- **Error Rate**: <1%
- **Throughput**: 1000+ requests/minute

### Алерты
- System health checks каждые 5 минут
- Telegram уведомления администратору
- Автоматический перезапуск сервисов
- Daily backups в 2:00 AM

## 💰 Бюджет

### Ежемесячные расходы
- **Smithery.ai Professional**: $29/месяц
- **External APIs**: ~$50/месяц
- **Инфраструктура**: Текущие расходы сервера

### Экономия
- Отказ от self-hosted MCP серверов
- Снижение операционных расходов
- Автоматизация процессов разработки

## 🛡️ Безопасность

### Меры безопасности
- SSL/TLS шифрование
- API rate limiting
- Input validation
- Secure environment variables
- Regular security updates

### Backup & Recovery
- Автоматические ежедневные бэкапы
- PostgreSQL database dumps
- Configuration files backup
- 7-дневная ротация бэкапов

## 🚦 Статус реализации

### ✅ Завершено
- [x] Создание полной документации
- [x] Setup скрипты для автоматической установки
- [x] Docker Compose конфигурация
- [x] n8n workflows примеры
- [x] Мониторинг и алерты скрипты

### 🔄 В процессе
- [ ] Регистрация на smithery.ai
- [ ] Получение API ключей
- [ ] Установка на production сервер

### 📋 Планируется
- [ ] Миграция Telegram Bot логики
- [ ] Интеграция MCP серверов
- [ ] Настройка мониторинга
- [ ] Production тестирование

## 📞 Поддержка

### Контакты
- **Администратор**: Telegram ID 299820674
- **Сервер**: ************* (root/3FBob4EtlWO1uF)
- **n8n**: https://chartgenius.ru/n8n/
- **Grafana**: https://chartgenius.ru/grafana/

### Полезные команды
```bash
# Проверка статуса сервисов
systemctl status n8n-chartgenius

# Просмотр логов
docker-compose logs -f n8n

# Создание бэкапа
/opt/n8n/backup.sh

# Мониторинг системы
/opt/n8n/monitor.sh
```

## 📝 Лицензия

Данный проект является частью ChartGenius платформы и предназначен для внутреннего использования.

---

**Последнее обновление**: 06.07.2025  
**Версия документации**: 1.0  
**Общий размер документации**: ~58KB (4 файла)
