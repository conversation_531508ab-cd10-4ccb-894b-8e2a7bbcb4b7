# ChartGenius MCP Integration - Deployment Scripts

## 1. Ubuntu Server Setup Script

### 1.1 Основной скрипт установки
```bash
#!/bin/bash
# setup_chartgenius_mcp.sh

set -e

echo "🚀 Setting up ChartGenius MCP Integration on Ubuntu 24.04 LTS"
echo "Server: *************"
echo "User: root"

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Node.js 20.x
echo "📦 Installing Node.js 20.x..."
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
node_version=$(node --version)
npm_version=$(npm --version)
echo "✅ Node.js installed: $node_version"
echo "✅ npm installed: $npm_version"

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    echo "📦 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    usermod -aG docker root
    rm get-docker.sh
else
    echo "✅ Docker already installed"
fi

# Install Docker Compose
echo "📦 Installing Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verify Docker installation
docker_version=$(docker --version)
compose_version=$(docker-compose --version)
echo "✅ Docker installed: $docker_version"
echo "✅ Docker Compose installed: $compose_version"

# Create n8n directory structure
echo "📁 Creating n8n directory structure..."
mkdir -p /opt/n8n/{data,config,workflows,backups}
mkdir -p /opt/n8n/data/{nodes,credentials}

# Install Smithery CLI globally
echo "📦 Installing Smithery CLI..."
npm install -g @smithery/cli

# Verify Smithery installation
smithery_version=$(smithery --version 2>/dev/null || echo "installed")
echo "✅ Smithery CLI installed: $smithery_version"

# Setup environment variables
echo "⚙️ Creating environment configuration..."
cat > /opt/n8n/.env << 'EOF'
# Smithery.ai Configuration
SMITHERY_API_KEY=your_smithery_api_key_here

# GitHub Integration
GITHUB_TOKEN=ghp_your_github_token_here

# Monitoring & Error Tracking
SENTRY_TOKEN=your_sentry_token_here
SENTRY_DSN=your_sentry_dsn_here
GRAFANA_API_KEY=your_grafana_api_key_here
GRAFANA_URL=https://chartgenius.grafana.net

# Design & UI
FIGMA_ACCESS_TOKEN=your_figma_token_here

# Oracle Database ChartGenius2
ORACLE_CONNECTION_STRING=your_oracle_connection_string
ORACLE_USERNAME=your_username
ORACLE_PASSWORD=your_password

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
ADMIN_TELEGRAM_ID=299820674

# Redis Cache
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=chartgenius_redis_2025

# FastAPI Backend
FASTAPI_URL=https://chartgenius.ru/api
FASTAPI_SECRET_KEY=your_secret_key

# External APIs
CRYPTOCOMPARE_API_KEY=your_cryptocompare_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# LLM Providers
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=chartgenius_n8n_2025
N8N_HOST=0.0.0.0
N8N_PORT=5678
N8N_PROTOCOL=https
N8N_EDITOR_BASE_URL=https://chartgenius.ru/n8n/
WEBHOOK_URL=https://chartgenius.ru/n8n/
GENERIC_TIMEZONE=Europe/Moscow
N8N_MCP_ENABLED=true
N8N_LOG_LEVEL=info
N8N_METRICS=true

# Security
N8N_SECURE_COOKIE=true
N8N_ENCRYPTION_KEY=your_encryption_key_here
EOF

echo "✅ Environment file created at /opt/n8n/.env"

# Create n8n MCP configuration
echo "⚙️ Creating MCP configuration..."
cat > /opt/n8n/config/mcp-servers.json << 'EOF'
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@smithery/github-mcp-server"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    },
    "sentry": {
      "command": "npx", 
      "args": ["-y", "@smithery/sentry-mcp-server"],
      "env": {
        "SENTRY_AUTH_TOKEN": "${SENTRY_TOKEN}",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    },
    "docker": {
      "command": "npx",
      "args": ["-y", "@smithery/docker-mcp-server"],
      "env": {
        "DOCKER_HOST": "unix:///var/run/docker.sock",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    },
    "grafana": {
      "command": "npx",
      "args": ["-y", "@smithery/grafana-mcp-server"],
      "env": {
        "GRAFANA_URL": "${GRAFANA_URL}",
        "GRAFANA_API_KEY": "${GRAFANA_API_KEY}",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    },
    "figma": {
      "command": "npx",
      "args": ["-y", "@smithery/figma-mcp-server"],
      "env": {
        "FIGMA_ACCESS_TOKEN": "${FIGMA_ACCESS_TOKEN}",
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["-y", "@smithery/playwright-mcp-server"],
      "env": {
        "SMITHERY_API_KEY": "${SMITHERY_API_KEY}"
      }
    }
  }
}
EOF

echo "✅ MCP servers configuration created"

# Create Docker Compose for n8n with MCP
echo "🐳 Creating Docker Compose configuration..."
cat > /opt/n8n/docker-compose.yml << 'EOF'
version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-chartgenius
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=${N8N_BASIC_AUTH_ACTIVE}
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - N8N_HOST=${N8N_HOST}
      - N8N_PORT=${N8N_PORT}
      - N8N_PROTOCOL=${N8N_PROTOCOL}
      - N8N_EDITOR_BASE_URL=${N8N_EDITOR_BASE_URL}
      - WEBHOOK_URL=${WEBHOOK_URL}
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
      - N8N_MCP_ENABLED=${N8N_MCP_ENABLED}
      - N8N_LOG_LEVEL=${N8N_LOG_LEVEL}
      - N8N_METRICS=${N8N_METRICS}
      - N8N_SECURE_COOKIE=${N8N_SECURE_COOKIE}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_password_2025
    volumes:
      - /opt/n8n/data:/home/<USER>/.n8n
      - /opt/n8n/config:/home/<USER>/.n8n/config
      - /opt/n8n/.env:/home/<USER>/.n8n/.env:ro
      - /var/run/docker.sock:/var/run/docker.sock
      - /opt/n8n/workflows:/home/<USER>/.n8n/workflows
      - /opt/n8n/backups:/home/<USER>/.n8n/backups
    depends_on:
      - postgres
      - redis
    networks:
      - n8n-network
      
  postgres:
    image: postgres:15-alpine
    container_name: postgres-n8n
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password_2025
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - n8n-network
      
  redis:
    image: redis:7-alpine
    container_name: redis-chartgenius
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - n8n-network

  # Monitoring stack
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-chartgenius
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=chartgenius_grafana_2025
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - n8n-network

networks:
  n8n-network:
    driver: bridge
      
volumes:
  postgres_data:
  redis_data:
  grafana_data:
EOF

echo "✅ Docker Compose configuration created"

# Create nginx configuration for reverse proxy
echo "🌐 Creating nginx configuration..."
cat > /opt/n8n/nginx.conf << 'EOF'
upstream n8n {
    server 127.0.0.1:5678;
}

upstream grafana {
    server 127.0.0.1:3000;
}

server {
    listen 80;
    server_name chartgenius.ru;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name chartgenius.ru;

    ssl_certificate /etc/letsencrypt/live/chartgenius.ru/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chartgenius.ru/privkey.pem;

    # n8n location
    location /n8n/ {
        proxy_pass http://n8n/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Increase timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Grafana location
    location /grafana/ {
        proxy_pass http://grafana/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Main application
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

echo "✅ Nginx configuration created"

# Create systemd service for n8n
echo "⚙️ Creating systemd service..."
cat > /etc/systemd/system/n8n-chartgenius.service << 'EOF'
[Unit]
Description=ChartGenius n8n with MCP Integration
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/n8n
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable n8n-chartgenius.service

echo "✅ Systemd service created and enabled"

# Create backup script
echo "💾 Creating backup script..."
cat > /opt/n8n/backup.sh << 'EOF'
#!/bin/bash
# ChartGenius n8n Backup Script

BACKUP_DIR="/opt/n8n/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="n8n_backup_$DATE"

echo "🔄 Starting backup: $BACKUP_NAME"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup n8n data
echo "📁 Backing up n8n data..."
cp -r /opt/n8n/data "$BACKUP_DIR/$BACKUP_NAME/"

# Backup configuration
echo "⚙️ Backing up configuration..."
cp -r /opt/n8n/config "$BACKUP_DIR/$BACKUP_NAME/"
cp /opt/n8n/.env "$BACKUP_DIR/$BACKUP_NAME/"
cp /opt/n8n/docker-compose.yml "$BACKUP_DIR/$BACKUP_NAME/"

# Backup workflows
echo "🔄 Backing up workflows..."
cp -r /opt/n8n/workflows "$BACKUP_DIR/$BACKUP_NAME/"

# Backup database
echo "🗄️ Backing up PostgreSQL database..."
docker exec postgres-n8n pg_dump -U n8n n8n > "$BACKUP_DIR/$BACKUP_NAME/n8n_database.sql"

# Create archive
echo "📦 Creating archive..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

# Keep only last 7 backups
echo "🧹 Cleaning old backups..."
ls -t *.tar.gz | tail -n +8 | xargs -r rm

echo "✅ Backup completed: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
EOF

chmod +x /opt/n8n/backup.sh

# Create cron job for daily backups
echo "⏰ Setting up daily backups..."
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/n8n/backup.sh >> /var/log/n8n-backup.log 2>&1") | crontab -

echo "✅ Daily backup cron job created"

# Create monitoring script
echo "📊 Creating monitoring script..."
cat > /opt/n8n/monitor.sh << 'EOF'
#!/bin/bash
# ChartGenius n8n Monitoring Script

LOG_FILE="/var/log/n8n-monitor.log"
TELEGRAM_BOT_TOKEN="${TELEGRAM_BOT_TOKEN}"
ADMIN_CHAT_ID="299820674"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

send_telegram_alert() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d chat_id="$ADMIN_CHAT_ID" \
        -d text="🚨 ChartGenius Alert: $message" \
        -d parse_mode="HTML"
}

# Check if n8n container is running
if ! docker ps | grep -q "n8n-chartgenius"; then
    log_message "ERROR: n8n container is not running"
    send_telegram_alert "n8n container is down! Attempting restart..."
    
    cd /opt/n8n
    docker-compose up -d
    
    sleep 30
    
    if docker ps | grep -q "n8n-chartgenius"; then
        log_message "INFO: n8n container restarted successfully"
        send_telegram_alert "n8n container restarted successfully ✅"
    else
        log_message "ERROR: Failed to restart n8n container"
        send_telegram_alert "Failed to restart n8n container ❌"
    fi
else
    log_message "INFO: n8n container is running"
fi

# Check if n8n is responding
if ! curl -s -f "http://localhost:5678" > /dev/null; then
    log_message "ERROR: n8n is not responding"
    send_telegram_alert "n8n is not responding on port 5678"
else
    log_message "INFO: n8n is responding"
fi

# Check disk space
DISK_USAGE=$(df /opt/n8n | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    log_message "WARNING: Disk usage is ${DISK_USAGE}%"
    send_telegram_alert "High disk usage: ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ "$MEMORY_USAGE" -gt 90 ]; then
    log_message "WARNING: Memory usage is ${MEMORY_USAGE}%"
    send_telegram_alert "High memory usage: ${MEMORY_USAGE}%"
fi

log_message "INFO: Monitoring check completed"
EOF

chmod +x /opt/n8n/monitor.sh

# Create cron job for monitoring (every 5 minutes)
echo "⏰ Setting up monitoring cron job..."
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/n8n/monitor.sh") | crontab -

echo "✅ Monitoring cron job created"

# Set proper permissions
echo "🔒 Setting permissions..."
chown -R root:root /opt/n8n
chmod -R 755 /opt/n8n
chmod 600 /opt/n8n/.env

echo ""
echo "🎉 ChartGenius MCP Integration setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Edit /opt/n8n/.env and add your API keys"
echo "2. Copy nginx configuration to /etc/nginx/sites-available/"
echo "3. Start the services: systemctl start n8n-chartgenius"
echo "4. Access n8n at: https://chartgenius.ru/n8n/"
echo "5. Access Grafana at: https://chartgenius.ru/grafana/"
echo ""
echo "🔐 Default credentials:"
echo "n8n: admin / chartgenius_n8n_2025"
echo "Grafana: admin / chartgenius_grafana_2025"
echo ""
echo "📊 Monitoring:"
echo "- Logs: /var/log/n8n-monitor.log"
echo "- Backups: /opt/n8n/backups/"
echo "- Daily backup at 2:00 AM"
echo "- Health check every 5 minutes"
echo ""
echo "✅ Setup completed successfully!"
EOF

chmod +x setup_chartgenius_mcp.sh

echo "✅ Setup script created and made executable"
echo "🚀 Run './setup_chartgenius_mcp.sh' to install ChartGenius MCP Integration"
```

## 2. Быстрый деплой скрипт

### 2.1 Одной командой
```bash
#!/bin/bash
# quick_deploy.sh - Быстрый деплой для ChartGenius MCP

echo "🚀 ChartGenius MCP Quick Deploy"
echo "Server: *************"

# Download and run setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/chartgenius-mcp/main/setup_chartgenius_mcp.sh | bash

# Start services
cd /opt/n8n
docker-compose up -d

echo "✅ Quick deploy completed!"
echo "🌐 Access n8n at: https://chartgenius.ru/n8n/"
```

## 3. Конфигурация для Augment Code MCP

### 3.1 Claude Desktop Config для n8n-mcp
```json
{
  "mcpServers": {
    "n8n-mcp": {
      "command": "node",
      "args": ["/path/to/n8n-mcp-server/dist/index.js"],
      "env": {
        "N8N_API_BASE_URL": "https://chartgenius.ru/n8n/api/v1",
        "N8N_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NTJiM2Y2OC03N2QxLTQ2NzEtOGZlNi00MmZiYTU5NWIyOWUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzUxNzkxMzM5fQ.-P9ojHL5B5izSwCe4zdCK_CpWhKalyX-lATO70nFxr4"
      }
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@context7/mcp-server"]
    },
    "sequential-thinking": {
      "command": "npx", 
      "args": ["-y", "@sequential-thinking/mcp-server"]
    }
  }
}
```
