# ChartGenius MCP Integration Plan: Hosted Solutions & n8n Orchestration

## Обзор стратегии

Данный план описывает переосмысленный подход к интеграции MCP в проект ChartGenius с использованием hosted решений на платформе smithery.ai и n8n в качестве центрального оркестратора приложения.

## 1. MCP как инструменты разработки

### 1.1 Анализ каталога smithery.ai для разработки

#### Анализ кода и архитектуры:
- **Semgrep MCP Server** - статический анализ кода и обнаружение уязвимостей
- **GitHub MCP Server** - управление репозиториями, pull requests, code review
- **GitLab MCP Server** - интеграция с GitLab для CI/CD процессов
- **Code Analysis MCP** - глубокий анализ кодовой базы
- **SonarQube MCP** - качество кода и технический долг

#### Автоматизация разработки:
- **Docker MCP Server** - управление контейнерами и образами
- **Kubernetes MCP** - оркестрация и деплой приложений
- **Terraform MCP** - Infrastructure as Code
- **GitHub Actions MCP** - автоматизация CI/CD pipeline
- **Jenkins MCP** - интеграция с Jenkins для автоматизации

#### Тестирование и отладка:
- **Playwright MCP** - автоматизированное тестирование UI
- **Selenium MCP** - веб-тестирование
- **Jest MCP** - unit тестирование JavaScript/TypeScript
- **PyTest MCP** - тестирование Python кода
- **Postman MCP** - API тестирование

#### Документирование:
- **Confluence MCP** - управление документацией
- **Notion MCP** - создание и поддержка документации
- **Markdown MCP** - генерация и обработка Markdown файлов
- **OpenAPI MCP** - автогенерация API документации

#### Мониторинг и профилирование:
- **Grafana MCP** - визуализация метрик
- **Prometheus MCP** - сбор и анализ метрик
- **Sentry MCP** - отслеживание ошибок
- **New Relic MCP** - мониторинг производительности
- **DataDog MCP** - комплексный мониторинг

#### UI/UX дизайн и разработка:
- **Figma MCP** - работа с дизайн-макетами
- **Storybook MCP** - разработка UI компонентов
- **Tailwind CSS MCP** - стилизация интерфейсов
- **React DevTools MCP** - отладка React приложений
- **Chrome DevTools MCP** - веб-разработка и отладка

### 1.2 Рекомендуемые MCP серверы для ChartGenius

#### Приоритет 1 (Критически важные):
1. **GitHub MCP Server** - управление кодом и версиями
2. **Docker MCP Server** - контейнеризация и деплой
3. **Sentry MCP** - мониторинг ошибок в production
4. **Grafana MCP** - мониторинг производительности
5. **Figma MCP** - работа с UI/UX дизайном

#### Приоритет 2 (Важные):
1. **Playwright MCP** - автоматизированное тестирование
2. **Semgrep MCP** - анализ безопасности кода
3. **Notion MCP** - документация проекта
4. **Terraform MCP** - управление инфраструктурой
5. **React DevTools MCP** - отладка frontend

#### Приоритет 3 (Полезные):
1. **Postman MCP** - API тестирование
2. **Jest MCP** - unit тестирование
3. **Confluence MCP** - техническая документация
4. **New Relic MCP** - детальный мониторинг
5. **Storybook MCP** - разработка компонентов

## 2. Архитектурная декомпозиция через n8n

### 2.1 Анализ текущей FastAPI архитектуры

#### Текущие роутеры и их функции:
- **analysis.py** - технический анализ криптовалют (25+ индикаторов)
- **admin.py** - административные функции, управление пользователями
- **mod.py** - модерационные функции, баны и флаги
- **watch.py** - управление watchlist пользователей
- **config.py** - конфигурация приложения
- **webhooks.py** - обработка внешних webhook'ов
- **user.py** - пользовательские функции

#### Текущие сервисы:
- **crypto_compare_provider.py** - получение данных от CryptoCompare API
- **data_processor.py** - обработка и расчет технических индикаторов
- **llm_router.py** - маршрутизация между OpenAI и Gemini
- **viz.py** - создание графиков и визуализации
- **oracle_client.py** - работа с Oracle AJD базой данных

### 2.2 Компоненты для вынесения в n8n workflows

#### Высокий приоритет (немедленно):
1. **Получение рыночных данных** - периодический сбор котировок
2. **Уведомления пользователей** - алерты и рассылки
3. **Обработка платежей** - Telegram Stars интеграция
4. **Мониторинг системы** - health checks и метрики
5. **Очистка данных** - архивирование старых записей

#### Средний приоритет (в течение месяца):
1. **Расчет технических индикаторов** - вынос тяжелых вычислений
2. **LLM анализ** - распределение нагрузки между провайдерами
3. **Генерация отчетов** - автоматические еженедельные отчеты
4. **Backup процедуры** - автоматическое резервное копирование
5. **Интеграция с внешними API** - CryptoCompare, Alpha Vantage

#### Низкий приоритет (долгосрочно):
1. **Пользовательская аналитика** - сбор метрик использования
2. **A/B тестирование** - эксперименты с UI/UX
3. **Автоматическое тестирование** - регрессионные тесты
4. **Документация** - автогенерация API docs
5. **Социальные функции** - интеграция с социальными сетями

### 2.3 Архитектура взаимодействия FastAPI ↔ n8n

#### Паттерн 1: Event-Driven Architecture
```python
# FastAPI отправляет события в n8n
async def trigger_analysis_workflow(symbol: str, user_id: str):
    webhook_url = "https://chartgenius.ru/n8n/webhook/analysis"
    payload = {
        "event": "analysis_requested",
        "symbol": symbol,
        "user_id": user_id,
        "timestamp": datetime.utcnow().isoformat()
    }
    async with httpx.AsyncClient() as client:
        await client.post(webhook_url, json=payload)
```

#### Паттерн 2: Request-Response через API
```python
# FastAPI запрашивает данные из n8n
async def get_processed_data(symbol: str):
    api_url = f"https://chartgenius.ru/n8n/api/data/{symbol}"
    async with httpx.AsyncClient() as client:
        response = await client.get(api_url)
        return response.json()
```

#### Паттерн 3: Queue-Based Processing
```python
# Использование Redis как очереди между FastAPI и n8n
async def queue_heavy_task(task_data: dict):
    redis_client.lpush("heavy_tasks", json.dumps(task_data))
    # n8n забирает задачи из очереди через Redis MCP
```

## 3. Интеграция с smithery.ai

### 3.1 Анализ smithery.ai платформы

#### Доступные опции подключения:
- **Hosted/Remote** - развернуты на инфраструктуре Smithery
- **Local** - установка через Smithery CLI на собственном сервере
- **Hybrid** - комбинация hosted и local решений

#### Преимущества hosted решений:
- Нет необходимости в собственной инфраструктуре
- Автоматические обновления и поддержка
- Высокая доступность и масштабируемость
- Встроенная система мониторинга

#### Ограничения hosted решений:
- Зависимость от внешнего провайдера
- Возможные ограничения по трафику/запросам
- Латентность сетевых запросов
- Ограниченная кастомизация

### 3.2 Стратегия подключения к smithery.ai

#### Этап 1: Базовая интеграция
1. **Регистрация на smithery.ai**
2. **Выбор тарифного плана** (рекомендуется Professional)
3. **Настройка аутентификации** через API ключи
4. **Подключение приоритетных MCP серверов**

#### Этап 2: Конфигурация n8n
```json
{
  "mcpServers": {
    "github": {
      "url": "https://api.smithery.ai/mcp/github",
      "auth": {
        "type": "bearer",
        "token": "${SMITHERY_API_KEY}"
      }
    },
    "sentry": {
      "url": "https://api.smithery.ai/mcp/sentry",
      "auth": {
        "type": "bearer", 
        "token": "${SMITHERY_API_KEY}"
      }
    }
  }
}
```

#### Этап 3: Мониторинг и оптимизация
- Настройка алертов на превышение лимитов
- Мониторинг латентности запросов
- Оптимизация частоты обращений к MCP серверам

### 3.3 Безопасность и аутентификация

#### Методы аутентификации:
1. **API Keys** - для базовой аутентификации
2. **OAuth 2.0** - для интеграций с GitHub, Google и др.
3. **JWT Tokens** - для внутренней аутентификации
4. **Webhook Signatures** - для верификации входящих данных

#### Рекомендации по безопасности:
- Использование environment variables для секретов
- Ротация API ключей каждые 90 дней
- Ограничение доступа по IP адресам
- Логирование всех обращений к MCP серверам

## 4. Конкретный план реализации

### 4.1 Подготовительный этап (1-2 недели)

#### Неделя 1: Анализ и планирование
- [ ] Регистрация на smithery.ai и изучение каталога
- [ ] Выбор тарифного плана и настройка аккаунта
- [ ] Анализ текущей архитектуры ChartGenius
- [ ] Определение приоритетных workflow для миграции

#### Неделя 2: Настройка инфраструктуры
- [ ] Обновление n8n до последней версии
- [ ] Настройка подключения к smithery.ai MCP серверам
- [ ] Создание тестовых workflow
- [ ] Настройка мониторинга и логирования

### 4.2 Этап 1: Базовые интеграции (2-3 недели)

#### Неделя 3: Мониторинг и уведомления
```json
{
  "workflow": "System Monitoring",
  "trigger": "Schedule (every 5 minutes)",
  "nodes": [
    {
      "name": "Health Check",
      "type": "HTTP Request",
      "url": "https://chartgenius.ru/health"
    },
    {
      "name": "Sentry Check",
      "type": "MCP",
      "server": "sentry",
      "action": "get_recent_errors"
    },
    {
      "name": "Alert Admin",
      "type": "Telegram",
      "condition": "if errors > 0"
    }
  ]
}
```

#### Неделя 4: Рыночные данные
```json
{
  "workflow": "Market Data Collection",
  "trigger": "Schedule (every 1 minute)",
  "nodes": [
    {
      "name": "Get Crypto Prices",
      "type": "HTTP Request",
      "url": "https://api.cryptocompare.com/data/pricemulti"
    },
    {
      "name": "Store in Oracle",
      "type": "Database",
      "connection": "oracle_ajd"
    },
    {
      "name": "Update Redis Cache",
      "type": "Redis",
      "action": "set"
    }
  ]
}
```

#### Неделя 5: Пользовательские уведомления
```json
{
  "workflow": "User Notifications",
  "trigger": "Database Change",
  "nodes": [
    {
      "name": "Check User Preferences",
      "type": "Database Query"
    },
    {
      "name": "Generate Alert Message",
      "type": "Code",
      "language": "javascript"
    },
    {
      "name": "Send Telegram Message",
      "type": "Telegram Bot API"
    }
  ]
}
```

### 4.3 Этап 2: Расширенная автоматизация (3-4 недели)

#### Неделя 6-7: Технический анализ
- Вынос расчета индикаторов в отдельные workflow
- Параллельная обработка множественных символов
- Кэширование результатов расчетов

#### Неделя 8-9: LLM интеграция
- Балансировка нагрузки между OpenAI и Gemini
- Retry логика при недоступности провайдеров
- Кэширование результатов анализа

### 4.4 Этап 3: Разработческие инструменты (4-5 недель)

#### Неделя 10-11: CI/CD автоматизация
```json
{
  "workflow": "Automated Testing",
  "trigger": "GitHub Push",
  "nodes": [
    {
      "name": "Run Tests",
      "type": "MCP",
      "server": "github-actions",
      "action": "trigger_workflow"
    },
    {
      "name": "Security Scan",
      "type": "MCP", 
      "server": "semgrep",
      "action": "scan_code"
    },
    {
      "name": "Deploy to Staging",
      "type": "MCP",
      "server": "docker",
      "action": "deploy_container"
    }
  ]
}
```

#### Неделя 12-13: Мониторинг и аналитика
- Интеграция с Grafana для метрик
- Настройка Sentry для отслеживания ошибок
- Автоматические отчеты о производительности

### 4.5 Этап 4: UI/UX улучшения (2-3 недели)

#### Неделя 14-15: Дизайн автоматизация
```json
{
  "workflow": "Design System Updates",
  "trigger": "Figma Webhook",
  "nodes": [
    {
      "name": "Export Design Tokens",
      "type": "MCP",
      "server": "figma",
      "action": "export_styles"
    },
    {
      "name": "Update CSS Variables",
      "type": "Code",
      "language": "javascript"
    },
    {
      "name": "Create Pull Request",
      "type": "MCP",
      "server": "github",
      "action": "create_pr"
    }
  ]
}
```

#### Неделя 16: Тестирование UI
- Автоматические скриншот тесты через Playwright
- Accessibility тестирование
- Performance мониторинг frontend

## 5. Ожидаемые результаты и метрики

### 5.1 Технические улучшения
- **Снижение нагрузки на FastAPI**: 50-70%
- **Улучшение времени отклика**: 30-50%
- **Увеличение надежности**: 99.9% uptime
- **Автоматизация процессов**: 80% рутинных задач

### 5.2 Разработческие улучшения
- **Ускорение разработки**: 40-60%
- **Снижение количества багов**: 50-70%
- **Улучшение качества кода**: автоматические проверки
- **Ускорение деплоя**: с часов до минут

### 5.3 Пользовательские улучшения
- **Более быстрые уведомления**: real-time алерты
- **Улучшенная аналитика**: персонализированные отчеты
- **Стабильность сервиса**: меньше простоев
- **Новые функции**: быстрое внедрение через workflow

## 6. Риски и митигация

### 6.1 Технические риски
- **Зависимость от smithery.ai**: резервные local MCP серверы
- **Латентность сети**: кэширование и оптимизация запросов
- **Ограничения API**: мониторинг лимитов и fallback стратегии

### 6.2 Бизнес риски
- **Увеличение расходов**: тщательное планирование бюджета
- **Сложность поддержки**: документирование всех процессов
- **Vendor lock-in**: возможность миграции на собственную инфраструктуру
